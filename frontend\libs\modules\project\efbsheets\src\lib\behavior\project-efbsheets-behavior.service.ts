/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, inject } from '@angular/core';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';
import { ConcreteMenuItem, ItemType } from '@libs/ui/common';
import { IPrjEfbsheetsCrewMixEntity } from '@libs/project/interfaces';
import { BasicsEfbsheetsCopyMasterCrewMixService } from '../copy-master-crewmix/basics-efb-sheets-copy-master-crew-mix.service';

@Injectable({
	providedIn: 'root'
})
export class ProjectEfbSheetsBehavior implements IEntityContainerBehavior<IGridContainerLink<IPrjEfbsheetsCrewMixEntity>, IPrjEfbsheetsCrewMixEntity> {
	private readonly basicsEfbsheetsCopyMasterCrewMixService = inject(BasicsEfbsheetsCopyMasterCrewMixService);

	public onCreate(containerLink: IGridContainerLink<IPrjEfbsheetsCrewMixEntity>): void {
		this.addItemsToToolbar(containerLink);
	}

	private addItemsToToolbar(containerLink: IGridContainerLink<IPrjEfbsheetsCrewMixEntity>) {
		const customToolbarItems: ConcreteMenuItem[] = [
			{
				caption: { key: 'basics.efbsheets.copyMasterCrewMix' },
				iconClass: ' tlb-icons ico-copy-line-item',
				type: ItemType.Item,
				sort: 1,
				fn: () => {
				    this.basicsEfbsheetsCopyMasterCrewMixService.showMasterCrewMixDialog();
				}
			}
		];

		containerLink.uiAddOns.toolbar.addItems(customToolbarItems);
	}
}
