/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectionToken } from '@libs/platform/common';
import { IIdentificationData } from '@libs/platform/common';
import { IEstCrewMixAfEntity, IEstCrewMixAfsnEntity, IBasicsEfbsheetsAverageWageEntity, IBasicsEfbsheetsCrewMixCostCodeEntity } from '@libs/basics/interfaces';

/**
 * Interface for project crew mix AF data service
 */
export interface IProjectCrewMixAfDataService {

	/**
	 * Get the current list of entities
	 */
	getListForInterface(): IEstCrewMixAfEntity[];
}

/**
 * Interface for project crew mix AFSN data service
 */
export interface IProjectCrewMixAfsnDataService {

	/**
	 * Get the current list of entities
	 */
	getListForInterface(): IEstCrewMixAfsnEntity[];
}

/**
 * Interface for project average wage data service
 */
export interface IProjectAverageWageDataService {

	/**
	 * Get the current list of entities
	 */
	getListForInterface(): IBasicsEfbsheetsAverageWageEntity[];
}

/**
 * Interface for project crew mix cost code data service
 */
export interface IProjectCrewMixCostCodeDataService {

	/**
	 * Get the current list of entities
	 */
	getListForInterface(): IBasicsEfbsheetsCrewMixCostCodeEntity[];

	/**
	 * Set modified state for an entity
	 */
	setModified(entity: any): void;
}

/**
 * Lazy injection tokens for project efbsheets services
 */
export const PROJECT_CREW_MIX_AF_DATA_SERVICE_TOKEN = new LazyInjectionToken<IProjectCrewMixAfDataService>('project.efbsheets.ProjectCrewMixAfDataService');

export const PROJECT_CREW_MIX_AFSN_DATA_SERVICE_TOKEN = new LazyInjectionToken<IProjectCrewMixAfsnDataService>('project.efbsheets.ProjectCrewMixAfsnDataService');

export const PROJECT_AVERAGE_WAGE_DATA_SERVICE_TOKEN = new LazyInjectionToken<IProjectAverageWageDataService>('project.efbsheets.ProjectAverageWageDataService');

export const PROJECT_CREW_MIX_COST_CODE_DATA_SERVICE_TOKEN = new LazyInjectionToken<IProjectCrewMixCostCodeDataService>('project.efbsheets.ProjectCrewMixCostCodeDataService');
