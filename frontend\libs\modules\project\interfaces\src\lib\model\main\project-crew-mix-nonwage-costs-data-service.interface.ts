/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectionToken } from '@libs/platform/common';
import { IIdentificationData } from '@libs/platform/common';
import { IEstNonwageCostsEntity } from '@libs/basics/interfaces';

/**
 * Interface for project crew mix nonwage costs data service
 */
export interface IProjectCrewMixNonwageCostsDataService {

	/**
	 * Get the current list of entities
	 */
	getListForInterface(): IEstNonwageCostsEntity[];
}

/**
 * Lazy injection token for project crew mix nonwage costs data service
 */
export const PROJECT_CREW_MIX_NONWAGE_COSTS_DATA_SERVICE_TOKEN = new LazyInjectionToken<IProjectCrewMixNonwageCostsDataService>('project.efbsheets.ProjectCrewMixNonwageCostsDataService');
