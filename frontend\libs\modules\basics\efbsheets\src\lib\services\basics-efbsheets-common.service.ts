/*
 * Copyright(c) RIB Software GmbH
 */

import { BasicsEfbsheetsDataService } from './basics-efbsheets-data.service';
import { BasicsEfbsheetsAverageWageDataService } from './basics-efbsheets-average-wage-data.service';
import { Injectable,inject } from '@angular/core';
import { BasicsEfbSheetsWageGroupLookupService } from '../basics-efbsheets-lookup/basics-efb-sheets-wage-group-lookup.service';
import { BasicsEfbSheetsSurchargeLookupService } from '../basics-efbsheets-lookup/basics-efb-sheets-surcharge-lookup-data.service';
import { BasicsEfbsheetsCrewMixAfDataService } from './basics-efbsheets-crew-mix-af-data.service';
import { BasicsEfbSheetsAdditionalCostLookupService } from '../basics-efbsheets-lookup/basics-efb-sheets-additional-cost-lookup-data.service';
import { BasicsEfbsheetsCrewMixAfsnDataService } from './basics-efbsheets-crew-mix-afsn-data.service';
import { BasicsEfbsheetsCrewMixCostCodeDataService } from './basics-efbsheets-crew-mix-cost-code-data.service';
import { BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN, childType, IBasicsEfbsheetsCommonService, IBasicsEfbsheetsEntity, ICostCodeEntity, IEstCrewMixAfEntity, IEstNonwageCostsEntity } from '@libs/basics/interfaces';
import { LazyInjectable, PlatformLazyInjectorService } from '@libs/platform/common';
import { BasicsEfbsheetsNonwageCostsDataService } from '../services/basics-efbsheets-nonwage-costs-data.service';
import { PROJECT_CREW_MIX_NONWAGE_COSTS_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_AF_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_AFSN_DATA_SERVICE_TOKEN, PROJECT_AVERAGE_WAGE_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_COST_CODE_DATA_SERVICE_TOKEN } from '@libs/project/interfaces';
 import { IEstCrewMixCostCodeEntity } from '@libs/basics/interfaces';

@Injectable({
	providedIn: 'root',
})
@LazyInjectable<IBasicsEfbsheetsCommonService>({
	token: BASICS_EFBSHEETS_COMMON_SERVICE_TOKEN,
	useAngularInjection: true,
})
export class BasicsEfbsheetsCommonService implements IBasicsEfbsheetsCommonService {
	private readonly basicsEfbsheetsAverageWageDataService = inject(BasicsEfbsheetsAverageWageDataService);
	private readonly basicsEfbSheetsWageGroupLookupService = inject(BasicsEfbSheetsWageGroupLookupService);
	private readonly basicsEfbsheetsDataService = inject(BasicsEfbsheetsDataService);
	private readonly basicsEfbSheetsSurchargeLookupService = inject(BasicsEfbSheetsSurchargeLookupService);
	private readonly basicsEfbsheetsCrewMixAfDataService = inject(BasicsEfbsheetsCrewMixAfDataService);
	private readonly basicsEfbSheetsAdditionalCostLookupService = inject(BasicsEfbSheetsAdditionalCostLookupService);
	private readonly basicsEfbsheetsCrewMixAfsnService = inject(BasicsEfbsheetsCrewMixAfsnDataService);
	private readonly basicsEfbsheetsCrewMixCostCodeDataService = inject(BasicsEfbsheetsCrewMixCostCodeDataService);
	private readonly basicsEfbsheetsNonwageCostsDataService = inject(BasicsEfbsheetsNonwageCostsDataService);
	private readonly lazyInjector = inject(PlatformLazyInjectorService);

	public calculateCrewmixesAndChilds(crewMixItem: IBasicsEfbsheetsEntity, childtype: childType, doUpdateRateHour: boolean = false): void {
		if (!crewMixItem) {
			return;
		}

		switch (childtype) {
			case childType.AverageWage:
				this.calculateBasedOnAverageWage(crewMixItem);
				break;
			case childType.CrewmixAF:
				this.calculateBasedOnCrewMixAF(crewMixItem, doUpdateRateHour);
				break;
			case childType.CrewmixAFSN:
				this.calculateBasedOnCrewMixAFSN(crewMixItem, doUpdateRateHour);
				break;
			case childType.CostCode:
				this.calculateBasedOnCrewmixCostCode(crewMixItem);
				break;

			case childType.NonwageCosts:
				this.calculateBasedOnNonwageCosts(crewMixItem);
				break;
		}
	}

	private async calculateBasedOnAverageWage(crewMixItem: IBasicsEfbsheetsEntity):Promise<void> {
		if (!crewMixItem) {
			return;
		}
		let allAverageWages;

		if (crewMixItem.ProjectFk) {
		  const projectAverageWageService = await this.lazyInjector.inject(PROJECT_AVERAGE_WAGE_DATA_SERVICE_TOKEN);
			allAverageWages = projectAverageWageService.getListForInterface();
		} else {
			allAverageWages = this.basicsEfbsheetsAverageWageDataService.getList();
		}

		crewMixItem.CrewSize = 0;
		crewMixItem.CrewAverage = 0;

		if (allAverageWages && Array.isArray(allAverageWages)) {
			allAverageWages.forEach(averageWageItem => {
				if (averageWageItem && typeof averageWageItem.Count === 'number') {
					if (!averageWageItem.Supervisory) {
						crewMixItem.CrewSize = (crewMixItem.CrewSize ?? 0) + averageWageItem.Count;
					}
					crewMixItem.CrewAverage = (averageWageItem.MarkupRate ?? 0) * averageWageItem.Count;
				}
			});
		}

		if ((crewMixItem.CrewSize ?? 1) > 0) {
			crewMixItem.CrewAverage = (crewMixItem.CrewAverage ?? 1) / (crewMixItem.CrewSize ?? 1);
		}

		crewMixItem.WageIncrease1 = crewMixItem.WageIncrease1 ? crewMixItem.WageIncrease1 : 0;
		crewMixItem.WageIncrease2 = crewMixItem.WageIncrease2 ? crewMixItem.WageIncrease2 : 0;

		crewMixItem.WageIncrease1 = ((crewMixItem.CrewAverage ?? 1) / 100) * crewMixItem.WageIncrease1;
		crewMixItem.WageIncrease2 = ((crewMixItem.CrewAverage ?? 1) / 100) * crewMixItem.WageIncrease2;

		crewMixItem.ExtraPay = crewMixItem.ExtraPay ? crewMixItem.ExtraPay : 0;
		crewMixItem.AverageStandardWage = (crewMixItem.CrewAverage ?? 0) + crewMixItem.WageIncrease1 + crewMixItem.WageIncrease2 + crewMixItem.ExtraPay;
	}

	private async calculateBasedOnCrewMixAF(crewMixItem: IBasicsEfbsheetsEntity, doUpdateRateHour: boolean): Promise<void> {

		if (!crewMixItem) {
			return;
		}
		let allCrewmixAfs: IEstCrewMixAfEntity[] = [], totalRateHour = 0;

		if (crewMixItem.ProjectFk) {
		const projectCrewMixAfService = await this.lazyInjector.inject(PROJECT_CREW_MIX_AF_DATA_SERVICE_TOKEN);
			allCrewmixAfs = projectCrewMixAfService.getListForInterface();
		} else {
			allCrewmixAfs = this.basicsEfbsheetsCrewMixAfDataService.getList();
		}

		 if (Array.isArray(allCrewmixAfs)) {
        allCrewmixAfs.forEach(crewmixAfItem => {
            if (crewmixAfItem && crewmixAfItem.MdcWageGroupFk) {
                crewmixAfItem.RateHour = doUpdateRateHour && crewmixAfItem.MarkupRate && crewmixAfItem.PercentHour && crewMixItem.AverageStandardWage
                    ? (crewMixItem.AverageStandardWage * (crewmixAfItem.MarkupRate / 100) * (crewmixAfItem.PercentHour / 100))
                    : crewmixAfItem.RateHour;
                totalRateHour += typeof crewmixAfItem.RateHour === 'number' ? crewmixAfItem.RateHour : 0;
            }
        });
    }

		crewMixItem.TotalSurcharge = typeof totalRateHour === 'number' && isFinite(totalRateHour) ? totalRateHour : 0;
		crewMixItem.CrewMixAf = (typeof crewMixItem.AverageStandardWage === 'number' ? crewMixItem.AverageStandardWage : 0) + crewMixItem.TotalSurcharge;

	}

	private async calculateBasedOnCrewMixAFSN(crewMixItem: IBasicsEfbsheetsEntity, doUpdateRateHour: boolean): Promise<void> {
		if (!crewMixItem) {
			return;
		}
		let allCrewmixAfsns, totalRateHour = 0;

		if (crewMixItem.ProjectFk) {
			const projectCrewMixAfsnService = await this.lazyInjector.inject(PROJECT_CREW_MIX_AFSN_DATA_SERVICE_TOKEN);
			allCrewmixAfsns = projectCrewMixAfsnService.getListForInterface();
		} else {
			allCrewmixAfsns = this.basicsEfbsheetsCrewMixAfsnService.getList();
		}

		if (allCrewmixAfsns && Array.isArray(allCrewmixAfsns)) {
			allCrewmixAfsns.forEach((crewmixAfsnItem) => {
				if (crewmixAfsnItem && crewmixAfsnItem.MdcWageGroupFk) {
					crewmixAfsnItem.RateHour = doUpdateRateHour && crewmixAfsnItem.MarkupRate && crewMixItem.CrewMixAf
						? (crewMixItem.CrewMixAf * (crewmixAfsnItem.MarkupRate / 100))
						: crewmixAfsnItem.RateHour;
					totalRateHour += (typeof crewmixAfsnItem.RateHour === 'number') ? crewmixAfsnItem.RateHour : 0;
				}
			});
		}

		crewMixItem.TotalExtraCost = (typeof totalRateHour === 'number') ? totalRateHour : 0;
		crewMixItem.CrewMixAfsn = (typeof crewMixItem.CrewMixAf === 'number' ? crewMixItem.CrewMixAf : 0) + (crewMixItem.TotalExtraCost ?? 0);

	}

	private async calculateBasedOnCrewmixCostCode(crewMixItem: IBasicsEfbsheetsEntity): Promise<void> {

		    let allCrewmixCostCodes: IEstCrewMixCostCodeEntity[] = [];
		if (crewMixItem.ProjectFk) {

			    const projectCrewMixCostCodeService = await this.lazyInjector.inject(PROJECT_CREW_MIX_COST_CODE_DATA_SERVICE_TOKEN);
			 allCrewmixCostCodes = projectCrewMixCostCodeService.getListForInterface();
		      
		} else {
			this.basicsEfbsheetsDataService.setModified(crewMixItem);
			 allCrewmixCostCodes = this.basicsEfbsheetsCrewMixCostCodeDataService.getList();
		}
	}

	public setSelectedLookupItem<T extends { MdcCostCodeFk?: number, Rate?: number }>(lookupItem: ICostCodeEntity, isProject: boolean, entities: T[]): void {
		let selectedEntity: T | null = null;

		if (lookupItem) {
			if (isProject) {
				if (entities.length > 0) {
					selectedEntity = entities[0];
					selectedEntity.MdcCostCodeFk = lookupItem.OriginalId ?? lookupItem.Id;
					selectedEntity.Rate = lookupItem.Rate ?? undefined;
				}
			} else {
				selectedEntity = entities[0];
				const selectedCrewMix2CostCodeItem = this.basicsEfbsheetsCrewMixCostCodeDataService.getSelectedEntity();
				if (selectedCrewMix2CostCodeItem) {
					selectedEntity.MdcCostCodeFk = lookupItem.Id;
					selectedCrewMix2CostCodeItem.Rate = lookupItem.Rate;
				}

			}
		}
	}
	
	private async calculateBasedOnNonwageCosts(crewMixItem: IBasicsEfbsheetsEntity): Promise<void> {

		if (!crewMixItem) {
			return;
		}

		let subTotal = 0;
		let allNonwageCosts: IEstNonwageCostsEntity[] = [];
		if (crewMixItem.ProjectFk) {
			const projectNonwageCostsService = await this.lazyInjector.inject(PROJECT_CREW_MIX_NONWAGE_COSTS_DATA_SERVICE_TOKEN);
			//await projectNonwageCostsService.loadForInterface({ id: crewMixItem.ProjectFk });
			allNonwageCosts = projectNonwageCostsService.getListForInterface();
		} else {
			allNonwageCosts = this.basicsEfbsheetsNonwageCostsDataService.getList();
		}

		allNonwageCosts.forEach((nonwageCostsItem) => {
			if (
				typeof nonwageCostsItem.Count === 'number' &&
				typeof nonwageCostsItem.RateDay === 'number'
			) {
				subTotal += nonwageCostsItem.Count * nonwageCostsItem.RateDay;
				nonwageCostsItem.TotalDay = nonwageCostsItem.Count * nonwageCostsItem.RateDay;
			}
		});

		const hour = typeof crewMixItem.HoursDay === 'number' ? crewMixItem.HoursDay : 0;
		const crewSize = typeof crewMixItem.CrewSize === 'number' ? crewMixItem.CrewSize : 0;
		const totalDailyCrewHours = hour * crewSize;

		crewMixItem.NonwageTotal = totalDailyCrewHours > 0 ? subTotal / totalDailyCrewHours : 0;
		crewMixItem.SocialPercent = typeof crewMixItem.SocialPercent === 'number' ? crewMixItem.SocialPercent : 0;
		crewMixItem.SocialHourPercent = typeof crewMixItem.SocialHourPercent === 'number' ? crewMixItem.SocialHourPercent : 0;
		crewMixItem.SocialTotal = crewMixItem.NonwageTotal * (crewMixItem.SocialPercent / 100) * (crewMixItem.SocialHourPercent / 100);

	}
}
