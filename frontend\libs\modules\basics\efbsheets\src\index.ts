import { IApplicationModuleInfo } from '@libs/platform/common';
import { BasicsEfbSheetsModuleInfo } from './lib/model/entities/basics-efbsheets-module-info.class';
export * from './lib/basics-efbsheets.module';
export * from './lib/model/wizard.class';
export * from './lib/model/entities/basics-efbsheets-module-info.class';
export * from './lib/model/entities/basics-efbsheets-complete.class';
export * from './lib/model/entities/basics-efbsheets-crew-mix-cost-code-complete.interface';
export * from './lib/model/entities/basics-efbsheets-crew-mix-af-complete.class';
export * from './lib/services/layout/basics-efbsheets-crew-mix-af-layout.service';
export * from './lib/services/basics-efbsheets-average-wage-data.service';
export * from './lib/services/basics-efbsheets-data.service';
export * from './lib/services/basics-efbsheets-common.service';
export * from './lib/services/layout/basics-efbsheest-crew-mix-average-wage-layout.service';
export * from './lib/services/layout/basics-efbsheets-crew-mix-afsn-layout.service';
export * from './lib/basics-efbsheets-lookup/basics-efb-sheets-additional-cost-lookup-data.service';
export * from './lib/basics-efbsheets-lookup/basics-efb-sheets-surcharge-lookup-data.service';
export * from './lib/basics-efbsheets-lookup/basics-efb-sheets-wage-group-lookup.service';
export * from './lib/basics-efbsheets-lookup/basics-efbsheets-nonwagecosts-lookup.service';
export * from './lib/basics-efbsheets-lookup/basics-efbsheets-lookup-provider.service';

export function getModuleInfo(): IApplicationModuleInfo {
    return BasicsEfbSheetsModuleInfo.instance;
}   
