/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { Observable, of, from, firstValueFrom } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { PlatformHttpService } from '@libs/platform/common';
import { IBasicsEfbsheetsEntity } from '@libs/basics/interfaces';
import { IGridConfiguration, IGridDialogOptions, UiCommonGridDialogService, FieldType, ItemType, IMenuItemEventInfo, IGridDialog, IdPropertyKeyOf } from '@libs/ui/common';
import { ProjectMainDataService } from '@libs/project/shared';
import { ProjectEfbsheetsDataService } from './project-efbsheets-data.service';

/**
 * Service for copying master crew mixes from basics to project crew mixes
 *
 * This service provides functionality similar to the AngularJS projectCrewMixCopyMasterCrewMixService:
 * - Shows a grid dialog with master crew mixes from basics/efbsheets/crewmixes/list
 * - Allows multi-selection of crew mixes
 * - Copies selected crew mixes to project using project/crewmix/crewmixes/saverequestdata
 * - Refreshes project data after successful copy
 *
 * Main entry point: showMasterCrewMixDialog()
 */
@Injectable({
	providedIn: 'root',
})
export class BasicsEfbsheetsCopyMasterCrewMixService {
	private readonly httpService = inject(PlatformHttpService);
	private readonly projectMainDataService = inject(ProjectMainDataService);
	private readonly projectEfbsheetsDataService = inject(ProjectEfbsheetsDataService);
	private readonly gridDialogService = inject(UiCommonGridDialogService);

	private selectedItems: IBasicsEfbsheetsEntity[] = [];
	private masterCrewMixItems: IBasicsEfbsheetsEntity[] = [];
	private searchPromise: Observable<IBasicsEfbsheetsEntity[]> | null = null;

	/**
	 * Get the standard grid configuration for the list view
	 */
	public getStandardConfigForListView(): IGridConfiguration<IBasicsEfbsheetsEntity> {
		return {
			uuid: 'b0b2f9bab961417d8ae14cfd260d624e',
			items: [],
		};
	}

	/**
	 * Get standard columns definition for master crew mix grid (similar to AngularJS version)
	 */
	public getStandardColumnsForListView() {
		return [
			{
				id: 'code',
				field: 'Code',
				name: 'Code',
				formatter: 'code',
				name$tr$: 'cloud.common.entityCode',
				width: 200
			},
			{
				id: 'desc',
				field: 'DescriptionInfo.Description',
				name: 'Description',
				name$tr$: 'cloud.common.entityDescription',
				formatter: 'description',
				width: 300
			}
		];
	}



	/**
	 * Get the list of master crew mix items asynchronously
	 */
	public getListAsync(forceRefresh: boolean = false): Observable<IBasicsEfbsheetsEntity[]> {
		if (!forceRefresh && this.masterCrewMixItems.length > 0) {
			return of(this.masterCrewMixItems);
		}

		return from(this.httpService.get<IBasicsEfbsheetsEntity[]>('basics/efbsheets/crewmixes/list'))
			.pipe(
				map((response) => {
					this.masterCrewMixItems = response || [];
					return this.masterCrewMixItems;
				}),
				catchError((error) => {
					console.error('Error loading master crew mix items:', error);
					return of([]);
				})
			);
	}

	/**
	 * Get search results for master crew mix items
	 */
	public getSearchList(filterValue: string): Observable<IBasicsEfbsheetsEntity[]> {
		if (!filterValue) {
			return this.getListAsync();
		}

		// Cancel previous search if still pending
		this.searchPromise = null;

		this.searchPromise = from(this.httpService.get<IBasicsEfbsheetsEntity[]>(
			`basics/efbsheets/crewmixes/listbyfilter?filterValue=${encodeURIComponent(filterValue)}`
		)).pipe(
			map((response) => response || []),
			catchError((error) => {
				console.error('Error searching master crew mix items:', error);
				return of([]);
			})
		);

		return this.searchPromise;
	}

	/**
	 * Select items for copying
	 */
	public selectItems(items: IBasicsEfbsheetsEntity[]): void {
		this.selectedItems = [...items];
	}

	/**
	 * Get selected items
	 */
	public getSelectedItems(): IBasicsEfbsheetsEntity[] {
		return this.selectedItems;
	}

	/**
	 * Copy selected master crew mixes to the project
	 */
	public copySelectedToProject(): Observable<boolean> {
		if (this.selectedItems.length === 0) {
			console.warn('No items selected for copying');
			return of(false);
		}

		const selectedProjectItem = this.projectMainDataService.getSelection()[0];
		if (!selectedProjectItem) {
			console.error('No project selected');
			return of(false);
		}

		const data = {
			EstCrewMixes: this.selectedItems,
			ProjectId: selectedProjectItem.Id,
		};

		return from(this.httpService.post('project/crewmix/crewmixes/saverequestdata', data))
			.pipe(
				map(() => {
					// Refresh the project crew mix data after successful copy
					this.projectEfbsheetsDataService.load({ id: selectedProjectItem.Id });
					return true;
				}),
				catchError((error) => {
					console.error('Error copying master crew mixes to project:', error);
					return of(false);
				})
			);
	}

	/**
	 * Clear the service data
	 */
	public clear(): void {
		this.masterCrewMixItems = [];
		this.selectedItems = [];
		this.searchPromise = null;
	}

	/**
	 * Set master crew mix items (for testing or external data)
	 */
	public setMasterCrewMixItems(items: IBasicsEfbsheetsEntity[]): void {
		this.masterCrewMixItems = [...items];
	}

	/**
	 * Get the list of master crew mix items (synchronous version)
	 * Returns empty array if no items are loaded
	 */
	public getList(): IBasicsEfbsheetsEntity[] {
		return this.masterCrewMixItems.length > 0 ? this.masterCrewMixItems : [];
	}

	/**
	 * Get master crew mix promise (similar to AngularJS version)
	 */
	public getEstCrewMixMasterPromise(): Promise<IBasicsEfbsheetsEntity[]> {
		return this.httpService.get<IBasicsEfbsheetsEntity[]>('basics/efbsheets/crewmixes/list');
	}

	/**
	 * Show master crew mix dialog using the grid dialog (main entry point)
	 * This replaces the AngularJS showMasterCrewMixDialog method
	 */
	public async showMasterCrewMixDialog(selectedProjectItem?: any): Promise<boolean> {
		// Use the current project if no specific project item is provided
		const projectItem = selectedProjectItem || this.projectMainDataService.getSelection()[0];
		if (!projectItem) {

			return false;
		}

		return this.openGridDialog();
	}



		public async openGridDialog(): Promise<boolean> {
		try {
			const masterCrewMixes = await firstValueFrom(this.getListAsync(false));
			console.log('Loaded master crew mixes:', masterCrewMixes);

			const gridDialogData: IGridDialogOptions<IBasicsEfbsheetsEntity> = {
				width: '80%',
				height: '70%',
				headerText: 'Master Crew Mixes',
				topDescription: '',
				windowClass: 'master-crew-mix-dialog',
				tools: {
					cssClass: 'tools',
					showImages: true,
					showTitles: false,
					isVisible: true,
					activeValue: '',
					overflow: false,
					iconClass: '',
					layoutChangeable: false,
					items: [
						{
							id: 'refresh',
							sort: 10,
							caption: 'Refresh',
							iconClass: 'tlb-icons ico-refresh',
							type: ItemType.Item,
							fn: async (info: IMenuItemEventInfo<IGridDialog<IBasicsEfbsheetsEntity>>) => {
								try {
									// Refresh the data
									const refreshedData = await this.httpService.get<IBasicsEfbsheetsEntity[]>('basics/efbsheets/crewmixes/list');
									// Update the grid data through the dialog context
									if (info.context && 'items' in info.context) {
										(info.context as any).items = refreshedData || [];
									}
								} catch (error) {
									console.error('Failed to refresh data:', error);
								}
							},
						},
					],
				},
				gridConfig: {
					uuid: 'b0b2f9bab961417d8ae14cfd260d624e',
					idProperty: 'Id' as IdPropertyKeyOf<IBasicsEfbsheetsEntity>,
					columns: [
						{
							type: FieldType.Code,
							id: 'Code',
							required: true,
							model: 'Code',
							maxLength: 16,
							label: {
								text: 'Code',
								key: 'cloud.common.entityCode',
							},
							visible: true,
							sortable: true,
						},
						{
							type: FieldType.Description,
							id: 'desc',
							required: false,
							model: 'DescriptionInfo.Description',
							label: {
								text: 'Description',
								key: 'cloud.common.entityDescription',
							},
							visible: true,
							sortable: true,
						}
						
					],
					 
				},
				items: masterCrewMixes || [],
				isReadOnly: false,
				allowMultiSelect: true,
				selectedItems: [],
				customButtons: [
				{
					id: 'refresh',
					caption: 'basics.common.updateCashFlowProjection.refreshBtnText',
					fn: (event, info) => {
				            this.getListAsync(true).subscribe((data) => {
				                if (info.dialog && 'items' in info.dialog) {
				                    (info.dialog as any).items = data || [];
				                }
				            });
					}
				}
			]
			};

			const result = await this.gridDialogService.show(gridDialogData);

			if (result?.closingButtonId === 'ok' && result.value?.selectedItems && result.value.selectedItems.length > 0) {
				const selectedProjectItem = this.projectMainDataService.getSelection()[0];
				if (!selectedProjectItem) {
					console.error('No project selected');
					return false;
				}

				const selectedItemIds = result.value.selectedItems;
				const selectedItems = result.value.items.filter(item => {
					const itemId = (item as IBasicsEfbsheetsEntity).Id;
					return itemId !== null && itemId !== undefined && selectedItemIds.includes(itemId);
				});

				console.log('Filtered selected items:', selectedItems);

				const data = {
					'EstCrewMixes': selectedItems,
					'ProjectId': selectedProjectItem.Id
				};

				try {
					await this.httpService.post('project/crewmix/crewmixes/saverequestdata', data);
					this.projectEfbsheetsDataService.load({ id: selectedProjectItem.Id });
					return true;
				} catch (error) {
					return false;
				}
			}

			return false;
		} catch (error) {
			return false;
		}
	}
}

