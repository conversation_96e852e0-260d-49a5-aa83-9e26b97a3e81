/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { PROJECT_COSTCODES_LOOKUP_PROVIDER_TOKEN } from '@libs/project/costcodes';
import { PROJECT_COSTCODES_MODULE_ADD_ON_TOKEN, PROJECT_DROP_POINTS_LOOKUP_PROVIDER_TOKEN, PROJECT_EFBSHEETS_MODULE_ADD_ON_TOKEN, PROJECT_AVERAGE_WAGE_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_AF_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_AFSN_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_COST_CODE_DATA_SERVICE_TOKEN, PROJECT_CREW_MIX_NONWAGE_COSTS_DATA_SERVICE_TOKEN, PROJECT_MAIN_LOOKUP_PROVIDER_TOKEN, PROJECT_MAIN_LAYOUT_SERVICE_TOKEN, PROJECT_MAIN_PRJ2BPCONTACT_LAYOUT_SERVICE_TOKEN, PROJECT_MAIN_PRJ2BP_LAYOUT_SERVICE_TOKEN, PROJECT_LOOKUP_PROVIDER_TOKEN } from '@libs/project/interfaces';



export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('project.costcodes.ProjectCostcodesModuleAddOn', PROJECT_COSTCODES_MODULE_ADD_ON_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/costcodes');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? new importedModule.ProjectCostcodesModuleAddOn() : null;
		
	}),
LazyInjectableInfo.create('project.costcodes.ProjectCostcodesLookupProvider', PROJECT_COSTCODES_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/costcodes');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectCostcodesLookupProvider) : null;
		
	}),
LazyInjectableInfo.create('project.droppoints.ProjectDropPointsLookupProviderService', PROJECT_DROP_POINTS_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/droppoints');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectDropPointsLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('project.efbsheets.ProjectEfbsheetsModuleAddOn', PROJECT_EFBSHEETS_MODULE_ADD_ON_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? new importedModule.ProjectEfbsheetsModuleAddOn() : null;
		
	}),
LazyInjectableInfo.create('project.efbsheets.ProjectEfbsheetsAverageWageDataService', PROJECT_AVERAGE_WAGE_DATA_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectEfbsheetsAverageWageDataService) : null;
		
	}),
LazyInjectableInfo.create('project.efbsheets.ProjectEfbsheetsCrewMixAfDataService', PROJECT_CREW_MIX_AF_DATA_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectEfbsheetsCrewMixAfDataService) : null;
		
	}),
LazyInjectableInfo.create('project.efbsheets.ProjectEfbsheetsCrewMixAfsnDataService', PROJECT_CREW_MIX_AFSN_DATA_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectEfbsheetsCrewMixAfsnDataService) : null;
		
	}),
LazyInjectableInfo.create('project.efbsheets.ProjectEfbsheetsCrewMixCostCodeDataService', PROJECT_CREW_MIX_COST_CODE_DATA_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectEfbsheetsCrewMixCostCodeDataService) : null;
		
	}),
LazyInjectableInfo.create('project.efbsheets.ProjectCrewMixNonwageCostsDataService', PROJECT_CREW_MIX_NONWAGE_COSTS_DATA_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/efbsheets');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectCrewMixNonwageCostsDataService) : null;
		
	}),
LazyInjectableInfo.create('project.main.ProjectLookupProviderService', PROJECT_MAIN_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/main');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('project.main.ProjectMainLayoutService', PROJECT_MAIN_LAYOUT_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/main');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectMainLayoutService) : null;
		
	}),
LazyInjectableInfo.create('project.main.ProjectMainPrj2BPContactLayoutService', PROJECT_MAIN_PRJ2BPCONTACT_LAYOUT_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/main');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectMainPrj2BPContactLayoutService) : null;
		
	}),
LazyInjectableInfo.create('project.main.ProjectMainPrj2BPLayoutService', PROJECT_MAIN_PRJ2BP_LAYOUT_SERVICE_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/main');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectMainPrj2BPLayoutService) : null;
		
	}),

	LazyInjectableInfo.create('project.shared.ProjectSharedProjectLookupProviderService', PROJECT_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/project/shared');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ProjectSharedProjectLookupProviderService) : null;
		
	}),
];
  